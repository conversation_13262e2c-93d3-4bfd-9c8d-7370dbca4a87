# Codebase

## 🚀 项目简介

Codebase通过融合BM25、向量嵌入和图索引等多种检索技术，为开发者提供精准、高效的代码搜索服务。

### ✨ 核心特性

- **🔍 多模态检索**：集成BM25关键词匹配、语义向量检索和代码依赖图检索
- **⚡ 智能增量更新**：实时监控代码变化，支持增量索引更新
- **🎯 语义理解**：基于最新大语言模型的代码语义理解
- **🔧 多语言支持**：支持Python、TypeScript、JavaScript等主流编程语言
- **📡 RESTful API**：提供完整的HTTP API接口
- **🔄 实时同步**：自动检测文件变化，保持索引与代码库同步

## 🏗️ 系统架构

```
Codebase
├── 🧠 核心组件
│   ├── IndexManager      # 索引管理器
│   ├── Pipeline         # 检索处理管道
│   ├── ModelFactory     # 模型工厂
│   └── ConfigManager    # 配置管理
│
├── 🔍 检索引擎
│   ├── BM25Retriever    # 关键词检索
│   ├── EmbeddingRetriever # 语义向量检索
│   ├── GraphRetriever   # 代码图检索
│   └── HybridRetriever  # 混合检索
│
├── 📊 索引系统
│   ├── BM25Index        # 倒排索引
│   ├── EmbeddingIndex   # 向量索引
│   └── GraphIndex       # 代码依赖图
│
└── 🌐 服务接口
    ├── FastAPI Server   # Web服务
    ├── Health Monitor   # 健康检查
    └── Auto Refresh     # 自动刷新
```

## 🛠️ 安装配置

### 环境要求

- Python 3.11+

### 快速安装

```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
pip install -r requirements.txt

# 复制配置文件
cp codecompass/config.example codecompass/config.py
```

### 配置说明

#### 详细配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `repo_path` | 代码仓库路径 | 必填 |
| `persist_path` | 索引持久化路径 | `./temp` |
| `host` | 服务器地址 | `0.0.0.0` |
| `port` | 服务器端口 | `5001` |
| `file_extensions` | 支持的文件类型 | `[".py", ".js", ".ts", ".ets"]` |
| `chunk_size` | 代码块大小 | `500` |
| `max_chunk_size` | 最大代码块大小 | `2000` |
| `refresh_interval` | 自动刷新间隔(秒) | `60` |

## 🚀 快速开始

### 启动服务

```bash
python codecompass/app.py \
  --repo-path /path/to/codebase \
  --embedding-api-key your-key \
  --llm-api-key your-key
```

## 📚 API 文档

### 核心接口

#### 1. 代码查询
```http
POST /query
Content-Type: application/json

{
  "query": "查询内容",
  "top_k": 10
}
```

**响应示例：**
```json
{
  "query": "export function HomeBuilder",
  "results": [
    {
      "metadata": {
        "file_path": "src/main/ets/pages/HomePage.ets",
        "start_line": 1,
        "end_line": 20
      },
      "file_path": "src/main/ets/pages/HomePage.ets",
      "text": "代码内容...",
      "score": 0.95
    }
  ]
}
```

#### 2. 实体搜索
```http
POST /search_entity
Content-Type: application/json

{
  "query": "实体名称",
  "top_n": 5
}
```

#### 3. 索引管理
```http
POST /indexes/update    # 手动更新索引
GET /indexes/status     # 查看索引状态
```

### 错误处理

所有API遵循标准HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `500`: 服务器内部错误

## 🔧 核心功能

### 1. 智能检索系统

- **BM25检索**：基于TF-IDF的关键词匹配
- **语义检索**：使用向量嵌入进行语义相似度匹配
- **图检索**：基于代码依赖关系的结构化检索

### 2. 增量索引更新

系统自动监控文件变化，支持三种更新模式：
- **新增文件**：自动添加到索引
- **修改文件**：增量更新相关索引
- **删除文件**：自动清理过期索引

### 3. 多语言代码解析

支持的编程语言：
- Python (.py)
- TypeScript (.ts, .ets)
- JavaScript (.js)
- 扩展支持更多语言

## 🎯 使用场景

### 代码搜索
```bash
# 搜索函数定义
"export function HomeBuilder"

# 搜索类实现
"class UserService"

# 搜索特定功能
"用户认证相关代码"
```

### 代码理解
```bash
# 理解代码逻辑
"TabBarData 是如何使用的"

# 查找调用关系
"getUserInfo 在哪里被调用"

# 分析依赖关系
"这个模块依赖了哪些组件"
```

## 🔬 开发指南

### 项目结构
```
codecompass/
├── app.py              # 主应用入口
├── config.py           # 配置管理
├── index_manager.py    # 索引管理器
├── pipeline_pro.py     # 检索管道
├── model_factory.py    # 模型工厂
├── retriever/          # 检索器模块
│   ├── bm25_retriever.py
│   ├── embedding_retriever.py
│   ├── graph_retriever.py
│   └── index/          # 索引实现
├── llm/               # 大语言模型
├── rerank/            # 结果重排序
└── workspace/         # 工作空间工具
```

## 🙏 致谢

- [LlamaIndex](https://github.com/run-llama/llama_index) - 核心检索框架
- [FastAPI](https://fastapi.tiangolo.com/) - Web框架
- [Sentence Transformers](https://www.sbert.net/) - 语义嵌入
- 所有贡献者和社区成员

