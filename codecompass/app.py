import os
import json
import sys
import logging
import asyncio
import time

# 添加当前目录到Python模块搜索路径，解决codeblocks模块导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from litellm import completion
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.logger import logger
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

try:
    from config import create_config
    from index_manager import IndexManager
    from pipeline_pro import Pipeline
    from model_factory import initialize_model_factory, get_model_factory
except ImportError:
    # 兼容运行时导入
    sys.path.append(os.path.dirname(__file__))
    from config import create_config
    from index_manager import IndexManager
    from pipeline_pro import Pipeline
    from model_factory import initialize_model_factory, get_model_factory

from contextlib import asynccontextmanager

# 全局配置和管理器
config = None
index_manager = None
pipeline = None

INTFACE_VERSION = "0.0.1"

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时的初始化已在main函数中完成
    yield
    # 应用关闭时的清理工作
    global index_manager
    if index_manager:
        index_manager.stop_auto_refresh()
        logger.info("应用关闭，已停止索引自动刷新")

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=False,  # 对于origin=null，不能设置为True
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],  # 明确允许的方法
    allow_headers=["*"],  # 允许所有请求头
    expose_headers=["*"],  # 暴露所有响应头
)

def setup_logging(log_level: str):
    """设置日志"""
    level = getattr(logging, log_level.upper())
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def initialize_app(app_config):
    """初始化应用"""
    global config, index_manager, pipeline
    
    config = app_config
    setup_logging(config.log_level)
    
    logger.info(f"初始化CodebaseQA服务")
    logger.info(f"仓库路径: {config.repo_path}")
    logger.info(f"持久化路径: {config.persist_path}")
    
    # 初始化模型工厂
    initialize_model_factory(config)
    
    # 初始化索引管理器
    index_manager = IndexManager(config)
    
    # 注册pipeline重新初始化回调函数
    index_manager.add_index_update_callback(reinitialize_pipeline)
    
    # 加载或构建索引
    logger.info("开始加载索引...")
    index_manager.load_indexes()

    # 启动自动刷新
    if config.index_config.enable_auto_refresh:
        index_manager.start_auto_refresh()
        logger.info("已启动索引自动刷新")

    # 初始化pipeline
    initialize_pipeline()
    
    logger.info("应用初始化完成")


def initialize_pipeline():
    """初始化pipeline"""
    global pipeline, index_manager
    
    # 获取pipeline路径
    pipeline_paths = index_manager.get_pipeline_paths()

    # 初始化pipeline
    pipeline = Pipeline(
        bm25_persist_path=pipeline_paths["bm25_persist_path"],
        emb_persist_path=pipeline_paths["emb_persist_path"],
        graph_persist_path=pipeline_paths["graph_persist_path"],
    )
    logger.info("Pipeline初始化完成")


def reinitialize_pipeline():
    """重新初始化pipeline（当索引更新时调用）"""
    global pipeline
    logger.info("检测到索引更新，重新加载Pipeline索引...")
    try:
        # 如果pipeline已存在，只需重新加载索引
        if pipeline:
            pipeline.reload_indexes()
            logger.info("Pipeline索引重新加载成功")
        else:
            # 如果pipeline不存在，则创建新的pipeline
            initialize_pipeline()
            logger.info("Pipeline重新初始化成功")
    except Exception as e:
        logger.error(f"Pipeline重新初始化失败: {e}")


def generate_context(query: str):
    results = pipeline.run(query)
    logger.debug(results)
    return {
        "query": query,
        "results": results,
    }


def _search_entity(query: str, top_n=None):
    """根据query的实体名搜索相关的实体和其相关的实体"""
    entity_searcher = pipeline.graph_retriever.entity_searcher
    dependency_searcher = pipeline.graph_retriever.dependency_searcher
    
    if not entity_searcher.has_node(query):
        return {"error": "entity not found"}
    
    neighbors = dependency_searcher.get_neighbors(query)[0]
    if isinstance(top_n, int):
        neighbors = neighbors[:top_n]
    neighbors.append(query)
    
    entities = entity_searcher.get_node_data(
        neighbors, return_code_content=True, wrap_with_ln=False
    )
    return {"entities": entities}


def paser_query_for_entity(query: str):
    """从query中解析出实体"""
    
    # 使用模型工厂获取LLM配置
    model_factory = get_model_factory()
    llm_config = model_factory.get_llm_config()
    
    # 设置litellm所需的环境变量
    os.environ["LM_STUDIO_API_BASE"] = llm_config.api_base
    os.environ["LM_STUDIO_API_KEY"] = llm_config.api_key
    
    response = completion(
        model=llm_config.model_name,
        messages=[
            {
                "role": "user",
                "content": query,
            }
        ],
    )
    return response.choices[0].message.content


@app.post("/query")
async def get_query_context(request: Request):
    request_data = await request.json()
    query = request_data["query"]
    top_k = request_data.get("top_k", 10)

    # 移除@codebase要求，直接处理查询
    if query.startswith("@codebase"):
        query = query.replace("@codebase", "").strip()
    
    try:
        results = pipeline.run(query, top_k=top_k)
        
        # 直接返回原始格式，保持与pipeline_pro.py中ResponseScheme一致
        context = {
            "query": query,
            "results": results
        }
        logger.debug("Generated context for query.")
        return JSONResponse(content=context)
    except Exception as e:
        logger.error(f"查询处理失败: {e}")
        return JSONResponse(
            content={"error": f"查询处理失败: {str(e)}"}, 
            status_code=500
        )


@app.post("/search_entity")
async def search_entity(request: Request):
    request_data = await request.json()
    query = request_data.get("query")

    if not query:
        return JSONResponse(
            content={"error": "Query parameter is required"}, status_code=400
        )

    top_n = request_data.get("top_n", None)
    entities = _search_entity(query, top_n)
    if "error" in entities:
        logger.debug(entities)
        entities = []

    data = {
        "interfaceVersion": INTFACE_VERSION,
        "query": {
            "text": query,
            "entity": "",
            "description": "",
            "requirement": "",
        },
        "entities": entities,
    }
    return JSONResponse(content=data)


@app.get("/runtime/v1")
def get_runtime_info():
    """获取运行时信息"""
    return {
        "repo_path": config.repo_path,
        "repo_name": config.repo_name,
        "persist_path": config.persist_path,
        "version": INTFACE_VERSION,
        "auto_refresh": config.index_config.enable_auto_refresh,
        "refresh_interval": config.index_config.refresh_interval,
    }


@app.get("/health")
def health_check():
    """健康检查"""
    try:
        # 检查pipeline是否正常
        pipeline_status = "healthy" if pipeline else "unhealthy"
        
        # 检查索引管理器状态
        index_status = "healthy" if index_manager else "unhealthy"
        
        return {
            "status": "healthy" if pipeline_status == "healthy" and index_status == "healthy" else "unhealthy",
            "version": INTFACE_VERSION,
            "repo_name": config.repo_name if config else "unknown",
            "repo_path": config.repo_path if config else "unknown",
            "pipeline_status": pipeline_status,
            "index_status": index_status,
            "timestamp": time.time()
        }
    except Exception as e:
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            },
            status_code=500
        )


@app.get("/metrics")
def get_metrics():
    """获取性能指标"""
    try:
        metrics = {
            "system": {
                "repo_path": config.repo_path,
                "repo_name": config.repo_name,
                "persist_path": config.persist_path,
                "auto_refresh_enabled": config.index_config.enable_auto_refresh,
                "refresh_interval": config.index_config.refresh_interval,
            },
            "indexes": {
                "bm25_path": config.bm25_persist_path,
                "embedding_path": config.embedding_persist_path,
                "graph_path": config.graph_persist_path,
            },
            "runtime": {
                "version": INTFACE_VERSION,
                "uptime": time.time() - start_time if 'start_time' in globals() else 0,
                "log_level": config.log_level,
            }
        }
        return JSONResponse(content=metrics)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取指标失败: {str(e)}"}, 
            status_code=500
        )


@app.get("/config")
def get_config():
    """获取系统配置"""
    try:
        config_data = {
            "repo_path": config.repo_path,
            "repo_name": config.repo_name,
            "persist_path": config.persist_path,
            "host": config.host,
            "port": config.port,
            "log_level": config.log_level,
            "file_extensions": config.file_extensions,
            "auto_refresh": {
                "enabled": config.index_config.enable_auto_refresh,
                "interval": config.index_config.refresh_interval
            },
            "index_config": {
                "chunk_lines": config.index_config.chunk_lines,
                "chunk_lines_overlap": config.index_config.chunk_lines_overlap,
                "max_chars": config.index_config.max_chars
            },
            "embedding_config": {
                "api_base": config.embedding_config.api_base,
                "model_name": config.embedding_config.model_name
            },
            "llm_config": {
                "api_base": config.llm_config.api_base,
                "model_name": config.llm_config.model_name
            }
        }
        return JSONResponse(content=config_data)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取配置失败: {str(e)}"}, 
            status_code=500
        )


@app.get("/indexes/status")
def get_indexes_status():
    """获取索引状态"""
    try:
        status = {
            "overall_status": "healthy",
            "indexes": {
                "bm25": {
                    "status": "healthy" if index_manager._check_bm25_index_exists() else "missing",
                    "path": config.bm25_persist_path
                },
                "embedding": {
                    "status": "healthy" if index_manager._check_embedding_index_exists() else "missing", 
                    "path": config.embedding_persist_path
                },
                "graph": {
                    "status": "healthy" if index_manager._check_graph_index_exists() else "missing",
                    "path": config.graph_persist_path
                }
            },
            "last_update": time.time(),
            "auto_refresh": config.index_config.enable_auto_refresh
        }
        
        # 检查整体状态
        if any(idx["status"] == "missing" for idx in status["indexes"].values()):
            status["overall_status"] = "degraded"
            
        return JSONResponse(content=status)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取索引状态失败: {str(e)}"}, 
            status_code=500
        )


@app.post("/indexes/update")
def update_indexes():
    """更新索引"""
    try:
        if index_manager.should_rebuild_index():
            logger.info("开始重建索引...")
            index_manager.build_all_indexes(incremental=True)
            
            # 重新加载索引
            index_manager.load_indexes()
            
            # 通知pipeline重新初始化
            index_manager._notify_index_update()
        else:
            logger.info("仓库CRC32未发生变化，不需要重建索引")
        return {
            "status": "success",
            "message": "索引更新完成",
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"重建索引失败: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "error": f"重建索引失败: {str(e)}",
                "timestamp": time.time()
            },
            status_code=500
        )


@app.get("/model/status")
def get_model_status():
    """获取模型状态"""
    try:
        model_factory = get_model_factory()
        
        status = {
            "embedding_model": {
                "api_base": config.embedding_config.api_base,
                "model_name": config.embedding_config.model_name,
                "status": "healthy"  # 简单状态检查
            },
            "llm_model": {
                "api_base": config.llm_config.api_base,
                "model_name": config.llm_config.model_name,
                "status": "healthy"  # 简单状态检查
            },
            "factory_status": "initialized" if model_factory else "error"
        }
        
        return JSONResponse(content=status)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取模型状态失败: {str(e)}"}, 
            status_code=500
        )


@app.get("/stats")
def get_stats():
    """获取统计信息"""
    try:
        stats = {
            "system_info": {
                "version": INTFACE_VERSION,
                "repo_name": config.repo_name,
                "uptime": time.time() - start_time if 'start_time' in globals() else 0
            },
            "index_stats": {
                "total_indexes": 3,
                "healthy_indexes": sum(1 for check in [
                    index_manager._check_bm25_index_exists(),
                    index_manager._check_embedding_index_exists(), 
                    index_manager._check_graph_index_exists()
                ] if check)
            },
            "performance": {
                "average_query_time": 0.5,  # TODO: 实际统计
                "total_queries": 0,  # TODO: 实际统计
                "error_rate": 0.0  # TODO: 实际统计
            }
        }
        
        return JSONResponse(content=stats)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取统计信息失败: {str(e)}"}, 
            status_code=500
        )


def main():
    """主函数"""
    global start_time
    start_time = time.time()
    
    try:
        # 创建配置（支持命令行参数和环境变量）
        app_config = create_config()
    except SystemExit as e:
        # --help 被调用
        if e.code == 0:
            sys.exit(0)
        raise
    except Exception as e:
        print(f"配置错误: {e}")
        sys.exit(1)
    
    # 初始化应用
    initialize_app(app_config)
    
    # 启动服务器
    logger.info(f"启动服务器: http://{config.host}:{config.port}")
    uvicorn.run(app, host=config.host, port=config.port)


if __name__ == "__main__":
    main()
