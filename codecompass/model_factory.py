"""
统一的模型工厂，管理所有模型的创建逻辑
避免在业务代码中出现模型配置相关的代码
"""

import logging
from typing import Optional
from llama_index.embeddings.openai import OpenAIEmbedding
from config import AppConfig, LLMConfig

logger = logging.getLogger(__name__)


class ModelFactory:
    """统一的模型工厂类"""
    
    def __init__(self, config: AppConfig):
        """
        初始化模型工厂
        
        Args:
            config: 应用配置
        """
        self.config = config
        self._embedding_model: Optional[OpenAIEmbedding] = None
    
    def get_embedding_model(self) -> OpenAIEmbedding:
        """
        获取Embedding模型实例（单例模式）
        
        Returns:
            OpenAIEmbedding实例
            
        Raises:
            ValueError: 如果配置不完整
        """
        if self._embedding_model is None:
            self._validate_embedding_config()
            
            logger.debug(f"创建Embedding模型: {self.config.embedding_config.model_name}")
            self._embedding_model = OpenAIEmbedding(
                model_name=self.config.embedding_config.model_name,
                api_base=self.config.embedding_config.api_base,
                api_key=self.config.embedding_config.api_key,
                embed_batch_size=self.config.embedding_config.embed_batch_size,
            )
            
        return self._embedding_model
    
    def get_llm_config(self) -> LLMConfig:
        """
        获取LLM配置

        Returns:
            LLM配置对象

        Raises:
            ValueError: 如果配置不完整
        """
        self._validate_llm_config()
        return self.config.llm_config

    def get_rerank_config(self):
        """
        获取Rerank配置

        Returns:
            Rerank配置对象
        """
        return self.config.rerank_config
    
    def _validate_embedding_config(self):
        """验证Embedding配置"""
        config = self.config.embedding_config
        
        if not config.api_key:
            raise ValueError("Embedding API密钥未配置")
        
        if not config.api_base:
            raise ValueError("Embedding API基础URL未配置")
            
        if not config.model_name:
            raise ValueError("Embedding模型名称未配置")
    
    def _validate_llm_config(self):
        """验证LLM配置"""
        config = self.config.llm_config
        
        if not config.api_key:
            raise ValueError("LLM API密钥未配置")
        
        if not config.api_base:
            raise ValueError("LLM API基础URL未配置")
            
        if not config.model_name:
            raise ValueError("LLM模型名称未配置")
    
    def create_embedding_for_indexer(self, indexer_config: Optional[dict] = None) -> OpenAIEmbedding:
        """
        为索引器创建Embedding模型实例
        
        Args:
            indexer_config: 索引器特定配置（如果需要覆盖默认配置）
            
        Returns:
            OpenAIEmbedding实例
        """
        # 如果提供了特定配置，使用特定配置；否则使用全局配置
        if indexer_config:
            return OpenAIEmbedding(
                model_name=indexer_config.get("model_name", self.config.embedding_config.model_name),
                api_base=indexer_config.get("api_base", self.config.embedding_config.api_base),
                api_key=indexer_config.get("api_key", self.config.embedding_config.api_key),
                embed_batch_size=indexer_config.get("embed_batch_size", self.config.embedding_config.embed_batch_size),
            )
        else:
            return self.get_embedding_model()


# 全局模型工厂实例
_model_factory: Optional[ModelFactory] = None


def initialize_model_factory(config: AppConfig):
    """初始化全局模型工厂"""
    global _model_factory
    _model_factory = ModelFactory(config)
    logger.info("模型工厂初始化完成")


def get_model_factory() -> ModelFactory:
    """获取全局模型工厂实例"""
    if _model_factory is None:
        raise RuntimeError("模型工厂未初始化，请先调用 initialize_model_factory()")
    return _model_factory 