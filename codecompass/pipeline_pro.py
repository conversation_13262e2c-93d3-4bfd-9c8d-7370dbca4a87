import logging
from retriever import BM25R<PERSON>rieverP<PERSON> as BM25Retriever
from retriever import EmbeddingRetrieverPro as EmbeddingRetriever
from rerank.dashscope_rerank import rerank
from pydantic import BaseModel
from retriever import GraphRetriever
import time


# 配置日志
logger = logging.getLogger(__name__)


# 如果没有配置根日志器，则进行基本配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s:%(lineno)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


class QueryAugment:
    def augment(self, query):
        return query


class ResponseScheme(BaseModel):
    metadata: dict
    file_path: str
    text: str
    score: float


def process_results(results):
    """
    处理检索结果，将其转换为 ResponseScheme 格式
    """
    processed_results = []
    for result in results:
        processed_result = ResponseScheme(
            metadata=result.metadata,
            file_path=result.metadata["file_path"],
            text=result.text,
            score=result.score,
        )
        processed_results.append(processed_result)
    return processed_results


class Pipeline:
    def __init__(
        self,
        bm25_persist_path: str = None,
        emb_persist_path: str = None,
        graph_persist_path: str = None,
    ) -> None:
        self.bm25_persist_path = bm25_persist_path
        self.emb_persist_path = emb_persist_path
        self.graph_persist_path = graph_persist_path

        self.bm25_retriever = None
        self.emb_retriever = None
        self.graph_retriever = None

        self.query_augment = QueryAugment()

        # 初始化加载索引
        self._load_indexes()

    def _load_indexes(self):
        """内部方法：加载所有索引"""
        if not self.bm25_persist_path:
            logger.error("未提供 BM25 持久化路径")
        else:
            self.bm25_retriever = BM25Retriever(self.bm25_persist_path)
            logger.info(
                f"成功从 {self.bm25_persist_path} 加载了索引, 启用BM25搜索"
            )

        if not self.emb_persist_path:
            logger.error("未提供 Embedding 持久化路径")
        else:
            self.emb_retriever = EmbeddingRetriever(self.emb_persist_path)
            logger.info(
                f"成功从 {self.emb_persist_path} 加载了索引, 启用embedding搜索"
            )

        if not self.graph_persist_path:
            logger.error("未提供 Graph 持久化路径")
        else:
            self.graph_retriever = GraphRetriever(self.graph_persist_path)
            logger.info(
                f"成功从 {self.graph_persist_path} 加载了索引, 启用graph搜索"
            )

    def reload_indexes(self):
        """重新加载所有索引"""
        logger.info("开始重新加载Pipeline索引...")
        try:
            # 清理旧的retriever
            self.bm25_retriever = None
            self.emb_retriever = None
            self.graph_retriever = None
            
            # 重新加载索引
            self._load_indexes()
            logger.info("Pipeline索引重新加载完成")
        except Exception as e:
            logger.error(f"Pipeline索引重新加载失败: {e}")
            raise

    @staticmethod
    def show_results(results):
        for item in results:
            logger.debug(item)

    def run(self, query, top_k=10):
        # step2 augment query
        query = self.query_augment.augment(query)
        # step3 retrieve
        bm25_results, emb_results = [], []
        if self.bm25_retriever:
            bm25_results = self.bm25_retriever.retrieve(query)
            logger.debug(
                f"BM25 results ({len(bm25_results)}):".center(100, "-")
            )
            self.show_results(bm25_results)
            logger.info(f"BM25检索到 {len(bm25_results)} 条结果")

        if self.emb_retriever:
            emb_results = self.emb_retriever.retrieve(query)
            logger.debug(
                f"Embedding results ({len(emb_results)}):".center(100, "-")
            )
            self.show_results(emb_results)
            logger.info(f"Embedding检索到 {len(emb_results)} 条结果")

        if self.graph_retriever:
            graph_results = self.graph_retriever.retrieve(query)
            logger.debug(
                f"Graph results ({len(graph_results)}):".center(100, "-")
            )
            self.show_results(graph_results)
            logger.info(f"Graph检索到 {len(graph_results)} 条结果")

        # step4 merge results
        results = bm25_results + emb_results
        
        # 去重：保留file_path+start_line+end_line相同的第一份结果
        seen_keys = set()
        unique_results = []
        for ret in results:
            if ret.metadata.get("file_path") is None:
                ret.metadata['file_path'] = ""
            if ret.metadata.get("start_line") is None:
                ret.metadata['start_line'] = 0
            if ret.metadata.get("end_line") is None:
                ret.metadata['end_line'] = 0
            # 构建唯一标识键
            key = f"{ret.metadata['file_path']}_{ret.metadata['start_line']}_{ret.metadata['end_line']}"
            if key not in seen_keys:
                seen_keys.add(key)
                unique_results.append(ret)
        results = unique_results
        logger.info(f"合并检索到 {len(results)} 条结果")

        # step4.5 融合结果：去除被包含的节点
        # 按文件路径分组
        file_groups = {}
        for ret in results:
            file_path = ret.metadata.get('file_path', '')
            if file_path not in file_groups:
                file_groups[file_path] = []
            file_groups[file_path].append(ret)
        
        # 对每个文件组进行融合处理
        filtered_results = []
        for file_path, group in file_groups.items():
            if len(group) <= 1:
                # 只有一个节点，直接添加
                filtered_results.extend(group)
                continue
                
            # 对当前文件的节点按start_line排序
            group.sort(key=lambda x: (x.metadata.get('start_line', 0), x.metadata.get('end_line', 0)))
            
            # 找出需要保留的节点（不被其他节点包含的节点）
            to_keep = []
            for i, current in enumerate(group):
                current_start = current.metadata.get('start_line', 0)
                current_end = current.metadata.get('end_line', 0)
                is_contained = False
                
                # 检查当前节点是否被其他节点包含
                for j, other in enumerate(group):
                    if i == j:
                        continue
                    other_start = other.metadata.get('start_line', 0)
                    other_end = other.metadata.get('end_line', 0)
                    
                    # 如果other节点包含current节点
                    if (other_start <= current_start and other_end >= current_end and 
                        not (other_start == current_start and other_end == current_end)):
                        is_contained = True
                        break
                
                if not is_contained:
                    to_keep.append(current)
            
            filtered_results.extend(to_keep)
        
        results = filtered_results
        logger.info(f"融合后保留 {len(results)} 条结果")

        # step4.6 按score降序排序
        results = sorted(results, key=lambda x: x.score, reverse=True)
        logger.info(f"结果已按score降序排序")

        # step5 sort results
        # results = sorted(results, key=lambda x: x.score, reverse=True)
        # documents = [item.get_text() for item in results]
        # logger.debug(f"Start rerank with {len(documents)} documents")
        # rerank_results = rerank(query, documents)
        # results = [results[index] for index, _ in rerank_results]

        # step6 process results
        results = process_results(results)

        # step6 return results
        results = [item.model_dump() for item in results]
        return results[:top_k]


if __name__ == "__main__":
    bm25_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/bm25_index_pro/chainlit"
    emb_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/embedding_index_pro/chainlit"
    pipeline = Pipeline(bm25_persist_path, emb_persist_path)
    query = "我想查询和ChainlitEmitter相关的内容"
    res = pipeline.run(query, top_k=10)
    from pprint import pprint

    pprint(res)
