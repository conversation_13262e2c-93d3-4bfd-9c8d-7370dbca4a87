import os
from .index import BM25<PERSON>ndexSimple, BM25IndexPro
from abc import ABC


class BM25Retriever(ABC):
    def __init__(self, persist_path: str) -> None:
        self.persist_path = persist_path
        self.index = None

    def pre_build(self, repo_path):
        documents = self.index.load_documents(repo_path)
        nodes = self.index.process_documents(documents)
        self.index.build_index(nodes)

    def retrieve(self, query, top_k=5) -> list:
        return self.index.retrieve(query, top_k)


class BM25RetrieverSimple(BM25Retriever):
    def __init__(self, persist_path: str) -> None:
        super().__init__(persist_path)
        self.index = BM25IndexSimple(persist_path)
        if os.path.exists(self.persist_path):
            self.index.load_index()


class BM25RetrieverPro(BM25Retriever):
    def __init__(self, persist_path: str) -> None:
        super().__init__(persist_path)
        self.index = BM25IndexPro(persist_path)
        if os.path.exists(self.persist_path):
            self.index.load_index()
