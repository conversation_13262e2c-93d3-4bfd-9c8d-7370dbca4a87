import os
from .index import EmbeddingIndexSimple, EmbeddingIndexPro
from abc import ABC


class EmbeddingRetriever(ABC):
    def __init__(self, persist_path: str) -> None:
        self.persist_path = persist_path
        self.index = None

    def pre_build(self, repo_path):
        documents = self.index.load_documents(repo_path)
        nodes = self.index.process_documents(documents)
        self.index.build_index(nodes)

    def retrieve(self, query, top_k=5) -> list:
        return self.index.retrieve(query, top_k)


class EmbeddingRetrieverSimple(EmbeddingRetriever):
    def __init__(self, persist_path: str) -> None:
        super().__init__(persist_path)
        self.index = EmbeddingIndexSimple(persist_path)
        if os.path.exists(self.persist_path):
            self.index.load_index()


class EmbeddingRetrieverPro(EmbeddingRetriever):
    def __init__(self, persist_path: str) -> None:
        super().__init__(persist_path)
        self.index = EmbeddingIndexPro(persist_path)
        if os.path.exists(self.persist_path):
            self.index.load_index()
