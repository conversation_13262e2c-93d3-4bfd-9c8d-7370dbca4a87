import logging
import os
import sys

from tree_sitter import Language
from pathlib import Path

# print(Path(__file__).parent.parent.parent.parent.parent.parent)
sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent.parent))

from codecompass.retriever.index.codeblocks.codeblocks import (
    CodeBlock,
    CodeBlockType,
    ReferenceScope,
    RelationshipType,
    ValidationError,
)
from codecompass.retriever.index.codeblocks.parser.parser import (
    CodeParser,
    NodeMatch,
    commented_out_keywords,
)

logger = logging.getLogger(__name__)


class ArkTSParser(CodeParser):
    def __init__(self, **kwargs):
        # 使用您提供的.so文件加载ArkTS语言
        language = Language(
            Path(__file__).parent / "build/my-languages-arkts.so",
            "arkts",
        )

        super().__init__(language, **kwargs)
        self.queries.extend(self._build_queries("arkts.scm"))

    @property
    def language(self):
        return "arkts"

    def pre_process(self, codeblock: CodeBlock, node_match: NodeMatch):
        # 处理ArkTS特有的语法
        if (
            codeblock.type == CodeBlockType.FUNCTION
            and codeblock.identifier == "build"
        ):
            # build方法是ArkTS组件的核心方法
            codeblock.type = CodeBlockType.FUNCTION

        # 处理@Component装饰器
        if codeblock.type == CodeBlockType.CLASS:
            # ArkTS的struct相当于类
            pass

        # 处理@State, @Prop等装饰器
        if codeblock.type == CodeBlockType.ASSIGNMENT:
            # 处理状态变量
            pass

    def post_process(self, codeblock: CodeBlock):
        # 检查是否为注释掉的代码
        if (
            codeblock.type == CodeBlockType.COMMENT
            and self.is_outcommented_code(codeblock.content)
        ):
            codeblock.type = CodeBlockType.COMMENTED_OUT_CODE

        # 处理this引用
        new_references: list = []
        for reference in codeblock.relationships:
            if reference.path and reference.path[0] == "this":
                struct_block = codeblock.find_type_in_parents(
                    CodeBlockType.CLASS
                )
                if struct_block:
                    reference.scope = ReferenceScope.CLASS
                    if len(reference.path) > 1:
                        reference.path = (
                            struct_block.full_path() + reference.path[1:2]
                        )
                        reference.identifier = codeblock.identifier

        codeblock.relationships.extend(new_references)

        # 检查重复的函数和struct名
        if (
            codeblock.type in [CodeBlockType.CLASS, CodeBlockType.FUNCTION]
            and len(codeblock.children) == 1
            and codeblock.children[0].type == CodeBlockType.COMMENTED_OUT_CODE
        ):
            codeblock.type = CodeBlockType.COMMENTED_OUT_CODE

        function_names = set()
        struct_names = set()
        for child in codeblock.children:
            if child.type == CodeBlockType.FUNCTION:
                if child.identifier in function_names:
                    child.validation_errors.append(
                        ValidationError(
                            error=f"Duplicate function name: {child.identifier}"
                        )
                    )
                function_names.add(child.identifier)
            if child.type == CodeBlockType.CLASS:
                if child.identifier in struct_names:
                    child.validation_errors.append(
                        ValidationError(
                            error=f"Duplicate struct name: {child.identifier}"
                        )
                    )
                struct_names.add(child.identifier)

    def is_outcommented_code(self, comment):
        return (
            comment.startswith("// ...")
            or comment.startswith("/* ...")
            or any(
                keyword in comment.lower() for keyword in commented_out_keywords
            )
        )


if __name__ == "__main__":
    ArkTSParser()
