(program . (_) @child.first @definition.module) @root

(interface_declaration
  (type_identifier) @identifier
) @root @definition.class

(struct_declaration
  (type_identifier) @identifier
) @root @definition.class

(class_declaration
  (type_identifier) @identifier
  (class_heritage
    (extends_clause
      (identifier) @reference.type
    )?
    (implements_clause
      (type_identifier) @reference.type
    )?
  )?
  (class_body
    ("{") @child.first
  )
) @root @definition.class

(function_declaration
  (identifier) @identifier
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
        (optional_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  (statement_block
    ("{") @child.first
  )
) @root @definition.function

(method_definition
  (property_identifier) @identifier
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
        (optional_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  (statement_block
    ("{") @child.first
  )
) @root @definition.function

(method_definition
  (property_identifier) @identifier
  (formal_parameters)
) @root @definition.function


(import_statement
  (import_clause
    [
      (identifier) @identifier
      (named_imports
        (import_specifier
          (identifier) @identifier
        )
      )
      (namespace_import
        (identifier) @identifier
      )
    ]
  )
  (string) @reference.module
) @root @definition.import

(export_statement
  [
    (function_declaration) @check_child
    (class_declaration) @check_child
    (interface_declaration) @check_child
    (struct_declaration) @check_child
    (variable_declaration) @check_child
  ]
) @root
