(program . (_) @child.first @definition.module) @root

; 类定义
(class_declaration
  (type_identifier) @identifier
  (class_heritage
    (extends_clause
      (identifier) @reference.type
    )?
    (implements_clause
      (type_identifier) @reference.type
    )?
  )?
  (class_body
    ("{") @child.first
  )
) @root @definition.class

; 接口定义
(interface_declaration
  (type_identifier) @identifier
  (extends_type_clause
    (type_identifier) @reference.type
  )?
  (object_type
    ("{") @child.first
  )
) @root @definition.class

; 枚举定义
(enum_declaration
  (identifier) @identifier
  (enum_body
    ("{") @child.first
  )
) @root @definition.class

; 函数定义
(function_declaration
  (identifier) @identifier
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
        (optional_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  (statement_block
    ("{") @child.first
  )
) @root @definition.function

; 方法定义
(method_definition
  (property_identifier) @identifier
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
        (optional_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  (statement_block
    ("{") @child.first
  )
) @root @definition.function

; 构造函数
(method_definition
  (property_identifier) @identifier
  (formal_parameters)
  (statement_block
    ("{") @child.first
  )
) @root @definition.constructor

; 箭头函数
(arrow_function
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  [
    (statement_block
      ("{") @child.first
    )
    (_) @child.first
  ]
) @root @definition.function

; 变量声明
(variable_declaration
  (variable_declarator
    (identifier) @identifier
    (type_annotation
      (_) @reference.type
    )?
    (_) @child.first
  )
) @root @definition.assignment

; 属性定义
(public_field_definition
  (property_identifier) @identifier
  (type_annotation
    (_) @reference.type
  )?
  (_) @child.first
) @root @definition.assignment

; Import 语句
(import_statement
  (import_clause
    [
      (identifier) @identifier
      (named_imports
        (import_specifier
          (identifier) @identifier
        )
      )
      (namespace_import
        (identifier) @identifier
      )
    ]
  )
  (string) @reference.module
) @root @definition.import

; Export 语句
(export_statement
  [
    (function_declaration) @check_child
    (class_declaration) @check_child
    (interface_declaration) @check_child
    (type_alias_declaration) @check_child
    (variable_declaration) @check_child
  ]
) @root

; 函数调用
(call_expression
  [
    (identifier) @reference.identifier
    (member_expression) @reference.identifier
  ]
  (arguments
    ("(")
    (
      [
        (identifier) @reference.identifier
        (_)
      ]
      (",")?
    )*
    (")")
  )
) @root @definition.call

; 注释
(comment) @root @definition.comment

; If 语句
(if_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

; For 语句
(for_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

; While 语句
(while_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

; Try-catch 语句
(try_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

(catch_clause
  (statement_block
    ("{") @child.first
  )
) @root @definition.dependent_clause

(finally_clause
  (statement_block
    ("{") @child.first
  )
) @root @definition.dependent_clause

; 通用语句块
(_
  (statement_block
    . ("{") @child.first
  )
) @root @definition.statement