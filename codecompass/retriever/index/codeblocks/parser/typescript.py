import logging

from tree_sitter_languages import get_language

from codecompass.retriever.index.codeblocks.codeblocks import (
    CodeBlock,
    CodeBlockType,
    ReferenceScope,
    RelationshipType,
    ValidationError,
)
from codecompass.retriever.index.codeblocks.parser.parser import (
    <PERSON><PERSON><PERSON><PERSON>,
    NodeMatch,
    commented_out_keywords,
)

logger = logging.getLogger(__name__)


class TypescriptParser(CodeParser):
    def __init__(self, **kwargs):
        language = get_language('typescript')

        super().__init__(language, **kwargs)
        self.queries.extend(self._build_queries('typescript.scm'))

    @property
    def language(self):
        return 'typescript'

    def pre_process(self, codeblock: CodeBlock, node_match: NodeMatch):
        # 处理构造函数
        if (
            codeblock.type == CodeBlockType.FUNCTION
            and codeblock.identifier == 'constructor'
        ):
            codeblock.type = CodeBlockType.CONSTRUCTOR

        # 处理 TypeScript 特有的语法
        if codeblock.type == CodeBlockType.ASSIGNMENT:
            # 处理类型注解
            pass

    def post_process(self, codeblock: CodeBlock):
        # 检查是否为注释掉的代码
        if codeblock.type == CodeBlockType.COMMENT and self.is_outcommented_code(
            codeblock.content
        ):
            codeblock.type = CodeBlockType.COMMENTED_OUT_CODE

        # 处理类型引用
        if codeblock.type == CodeBlockType.ASSIGNMENT:
            for reference in codeblock.relationships:
                reference.type = RelationshipType.TYPE

        # 处理 this 引用
        new_references: list = []
        for reference in codeblock.relationships:
            if reference.path and reference.path[0] == 'this':
                class_block = codeblock.find_type_in_parents(CodeBlockType.CLASS)
                if class_block:
                    reference.scope = ReferenceScope.CLASS
                    if len(reference.path) > 1:
                        reference.path = class_block.full_path() + reference.path[1:2]
                        reference.identifier = codeblock.identifier

            # 处理 super 引用
            if reference.path and reference.path[0] == 'super':
                class_block = codeblock.find_type_in_parents(CodeBlockType.CLASS)
                if class_block:
                    is_a_rel = [
                        rel
                        for rel in class_block.relationships
                        if rel.type == RelationshipType.IS_A
                    ]
                    if is_a_rel:
                        super_class = codeblock.root().find_by_path(is_a_rel[0].path)
                        if super_class:
                            reference.path = (
                                super_class.full_path() + reference.path[1:2]
                            )
                            reference.identifier = super_class.identifier

        codeblock.relationships.extend(new_references)

        # 检查重复的函数和类名
        if (
            codeblock.type in [CodeBlockType.CLASS, CodeBlockType.FUNCTION]
            and len(codeblock.children) == 1
            and codeblock.children[0].type == CodeBlockType.COMMENTED_OUT_CODE
        ):
            codeblock.type = CodeBlockType.COMMENTED_OUT_CODE

        function_names = set()
        class_names = set()
        for child in codeblock.children:
            if child.type == CodeBlockType.FUNCTION:
                if child.identifier in function_names:
                    child.validation_errors.append(
                        ValidationError(
                            error=f'Duplicate function name: {child.identifier}'
                        )
                    )
                function_names.add(child.identifier)
            if child.type == CodeBlockType.CLASS:
                if child.identifier in class_names:
                    child.validation_errors.append(
                        ValidationError(
                            error=f'Duplicate class name: {child.identifier}'
                        )
                    )
                class_names.add(child.identifier)

    def is_outcommented_code(self, comment):
        return comment.startswith('// ...') or comment.startswith('/* ...') or any(
            keyword in comment.lower() for keyword in commented_out_keywords
        )