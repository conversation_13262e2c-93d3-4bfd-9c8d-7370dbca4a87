from utils.epic_split import EpicSplitter

if __name__ == "__main__":
    from typing import Dict
    import fnmatch
    import os
    import mimetypes
    from llama_index.core import SimpleDirectoryReader

    repo_path = "/Users/<USER>/projects/auto-code-rover"

    def file_metadata_func(file_path: str) -> Dict:
        # print(file_path)
        file_path = file_path.replace(repo_path, "")
        if file_path.startswith("/"):
            file_path = file_path[1:]


        ignore_patterns = [
            "**/__pycache__/**",
            "**/.pytest_cache/**",
            "**/.coverage/**",
            "**/.coverage.*",
            "**/.coverage.*",
            "**/venv/**",
            "**/node_modules/**",
            "**/dist/**",
            "**/build/**",
            "**/cache/**",
            "**/logs/**",
            "**/temp/**",
            "**/test/**",
            "**/tests/**",
            "**/test_*.py",
            "**/*_test.py",
        ]
        category = (
            "test"
            if any(
                fnmatch.fnmatch(file_path, pattern) for pattern in ignore_patterns
            )
            else "implementation"
        )

        return {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "file_type": mimetypes.guess_type(file_path)[0],
            "category": category,
        }

    reader = SimpleDirectoryReader(
        input_dir=repo_path,
        exclude=[
            "**/test/**",
            "**/tests/**",
            "**/test_*.py",
            "**/*_test.py",
        ],
        file_metadata=file_metadata_func,
        filename_as_id=True,
        required_exts=[".py"],  # TODO: Shouldn't be hardcoded and filtered
        recursive=True,
    )
    docs = reader.load_data()
    splitter = EpicSplitter(
        repo_path="/Users/<USER>/projects/auto-code-rover",
    )
    prepared_nodes = splitter.get_nodes_from_documents(docs, show_progress=True)
