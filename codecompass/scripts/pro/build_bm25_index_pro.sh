
# 检查变量是否为空
if [ -z "$REPO_PATH" ] || [ -z "$PERSIST_PATH" ]; then
    echo "Error: REPO_PATH and PERSIST_PATH must be set."
    exit 1
fi

DEFAULT_NAME="bm25_index_pro"
filename=$(basename "$REPO_PATH")
bm25_persist_path="$PERSIST_PATH/$DEFAULT_NAME/$filename"


python retriever/index/bm25_index_pro.py \
        --repo_path $REPO_PATH \
        --persist_path $bm25_persist_path \
        --chunk_lines 100 \
        --chunk_lines_overlap 15 \
        --max_chars 1500
