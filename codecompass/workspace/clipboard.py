import pyperclip
import sys
from typing import Optional

def get_clipboard_content() -> Optional[str]:
    """
    Get content from clipboard.
    
    Returns:
        str: The clipboard content, or None if clipboard is empty or error occurs
    """
    try:
        content = pyperclip.paste()
        if content and content.strip():
            return content.strip()
        return None
    except Exception as e:
        print(f"Error reading from clipboard: {e}", file=sys.stderr)
        return None

def print_clipboard_context():
    """
    Print the current clipboard content as context.
    """
    content = get_clipboard_content()
    if content:
        print("Clipboard Context:")
        print("-" * 50)
        print(content)
        print("-" * 50)
    else:
        print("No content found in clipboard or clipboard is empty.")

if __name__ == "__main__":
    print_clipboard_context()